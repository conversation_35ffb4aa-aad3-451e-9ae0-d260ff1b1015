# "法弈"智能系统展示网页

## 项目简介

这是一个基于开发文档要求创建的"法弈"智能系统展示网页，旨在以简洁、美观且富有科技感的方式展示AI法律决策助手的核心功能与价值。

## 功能特性

### 🎯 核心功能
- **案情智能解构** - 智能分析案件情况
- **诉讼文书一键生成** - 自动生成标准法律文书
- **多维诉讼风险评估** - 全面评估案件风险
- **专业法律策略指引** - 提供专业的法律建议

### 🎨 设计特色
- **单页应用体验** - 流畅的页面切换和滚动体验
- **响应式设计** - 完美适配桌面、平板和手机设备
- **科技感视觉** - 现代化的UI设计和动画效果
- **交互式演示** - 真实的用户操作体验

### 📱 页面结构

#### 1. 系统介绍区 (Hero Section)
- 醒目的品牌标题和标语
- 四大核心价值展示
- 动态背景效果
- 引导用户开始体验

#### 2. 智能交互区 (Interactive Demo)
- 步骤化的用户引导
- 案情描述输入框 (D_text)
- 诉讼诉求输入框 (C_claim)
- 示例案例一键填充
- 智能分析处理动画

#### 3. 成果展示区 (Results Showcase)
- **起诉文书初稿**
  - 标准法律文书格式
  - 一键复制和下载功能
  - 关键信息高亮显示

- **综合性分析报告**
  - 案件优势评估 (78%)
  - 关键证据缺失诊断
  - 法律条款和案例指引
  - 模拟法庭推演
  - 策略优化建议

#### 4. 价值总结区 (Footer)
- 系统价值重申
- 联系方式信息
- 版权和法律声明

## 技术实现

### 前端技术栈
- **HTML5** - 语义化标记
- **CSS3** - 现代样式和动画
- **JavaScript (ES6+)** - 交互逻辑和动态效果
- **Font Awesome** - 图标库
- **Google Fonts** - 中文字体支持

### 核心特性
- 📱 完全响应式设计
- 🎭 CSS3动画和过渡效果
- 🔄 平滑滚动和页面导航
- 📊 数据可视化展示
- 🎯 交互式用户体验
- ⚡ 性能优化和加载动画

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 文件结构

```
fayi/web/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑
├── 示例.json           # 示例数据
├── 网页开发文档.md      # 开发文档
└── README.md           # 项目说明
```

## 使用说明

### 本地运行
1. 直接在浏览器中打开 `index.html` 文件
2. 或使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

### 体验流程
1. 点击"开始体验"按钮进入交互区
2. 输入案情描述或点击"使用示例案例"
3. 填写诉讼诉求或选择常见诉求标签
4. 点击"立即分析"开始智能分析
5. 查看生成的起诉文书和分析报告
6. 使用复制、下载等功能

## 数据说明

项目使用 `示例.json` 中的模拟数据，包含：
- 用户输入的案情描述和诉求
- 系统生成的法律文书
- 详细的案件分析报告
- 证据链分析和策略建议

## 开发说明

### 设计原则
- **简洁美学** - 大量留白，统一色调
- **引导式交互** - 清晰的用户操作流程
- **数据可视化** - 图表和摘要展示
- **科技感体验** - 现代化的视觉效果

### 主要颜色
- 主色调：蓝紫渐变 (#667eea - #764ba2)
- 强调色：金黄色 (#ffd700)
- 背景色：白色和浅灰 (#f8f9fa)
- 文字色：深灰 (#333)

### 响应式断点
- 大屏幕：1024px+
- 平板：768px - 1023px
- 手机：480px - 767px
- 小屏手机：< 480px

## 更新日志

### v1.0.0 (2025-08-24)
- ✅ 完成基础页面结构
- ✅ 实现四大核心区域
- ✅ 添加响应式设计
- ✅ 集成示例数据
- ✅ 优化交互体验
- ✅ 添加动画效果

## 许可证

本项目仅用于展示和学习目的。

---

**"法弈"智能系统 - 让法律服务触手可及** 🏛️⚖️
