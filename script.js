// 示例数据 - 从示例.json文件中提取
const exampleData = {
    "userInput": {
        "D_text": "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫"幻影X Pro"的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！我找他们客服理论，客服就一直踢皮球，翻来覆去就说是我自己装了什么不兼容的软件，还让我上传日志、拍视频，折腾了好半天，最后就说只给修，不给退不给换，修还要一个月！这不明摆着是电脑本身就有毛病吗？我坚决不同意维修，我就要退货退款！这事儿太耽误我工作了！",
        "C_claim": "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！"
    },
    "systemOutput": {
        "L_document": {
            "title": "民事起诉状",
            "plaintiff": {
                "label": "原告",
                "name": "张三",
                "gender": "男",
                "birthDate": "1995年10月20日",
                "ethnicity": "汉族",
                "idNumber": "11010119951020XXXX",
                "address": "北京市朝阳区建国路XX号院X号楼X单元XXX室",
                "phone": "138-0013-8000"
            },
            "defendant": {
                "label": "被告",
                "name": "未来科技电脑有限公司",
                "creditCode": "91440300MA5GXXXXXX",
                "address": "广东省深圳市南山区科技园路XX号",
                "legalRepresentative": "李四",
                "representativeTitle": "法定代表人",
                "phone": "0755-88886666"
            },
            "caseCause": "买卖合同纠纷",
            "claims": {
                "title": "诉讼请求",
                "items": [
                    "一、判令被告立即退还原告购物款人民币12,888元；",
                    "二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；",
                    "三、判令被告承担本案全部诉讼费用。"
                ]
            },
            "factsAndReasons": {
                "title": "事实与理由",
                "sections": [
                    {
                        "heading": "一、消费事实基本情况",
                        "content": "原告于2025年7月15日，通过被告运营的官方网站（www.futuretech.com）购买了"幻影X Pro"型笔记本电脑一台，并通过在线支付方式支付货款共计人民币12,888元，相关订单号为880123456789。该商品由被告安排物流并于同年7月18日送达原告处，双方之间的买卖合同关系依法成立且生效。"
                    }
                ]
            }
        },
        "R_analysis": {
            "reportSummary": {
                "caseStrengthScore": 78,
                "keyEvidenceGaps": 1,
                "strategySuggestions": 3
            },
            "evidenceAnalysis": {
                "gapDiagnosis": [
                    "关键证据缺失：当前证据主要依赖原告单方陈述和记录，虽能构成初步证据链，但被告可能抗辩称故障系人为原因导致。一份由第三方权威机构出具的质检报告，将是锁定被告责任、反驳其抗辩理由的核心证据，建议尽快获取。"
                ]
            },
            "legalInsights": {
                "relevantStatutes": [
                    {
                        "statuteName": "《中华人民共和国消费者权益保护法》第二十四条",
                        "content": "经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务..."
                    }
                ],
                "guidingCases": [
                    {
                        "caseTitle": "王某诉某电子品牌商产品责任纠纷案",
                        "caseNumber": "（2024）沪01民终XXXX号",
                        "judgmentSummary": "法院认为，消费者购买的产品在"三包"期内出现非人为性能故障，经两次修理仍不能正常使用的，经营者应当负责更换或者退货。"
                    }
                ]
            },
            "courtSimulation": {
                "adversarialSummary": {
                    "ourArguments": [
                        "产品在三包期内出现严重性能故障，符合法定退货条件。",
                        "被告以不实理由（如软件不兼容）推卸责任，属于恶意违约。",
                        "产品质量问题直接导致了原告的误工损失，应予赔偿。"
                    ],
                    "opponentDefenses": [
                        "故障可能是由原告自行安装的第三方软件或病毒导致，非产品本身质量问题。",
                        "原告未能提供权威的检测报告证明产品存在固有缺陷。",
                        "原告主张的误工损失缺乏充分的证据支持。"
                    ]
                },
                "strategyOptimization": [
                    "首要策略：立即将设备送至具有司法鉴定资质的第三方检测机构进行检测，获取产品存在质量缺陷的直接证据。",
                    "次要策略：整理并公证与客服的完整沟通记录，固定被告消极应对和推诿责任的证据。",
                    "补充策略：准备误工损失的相关证据，如劳动合同、请假记录或因设备问题导致项目延误的沟通邮件等，以增强索赔请求的说服力。"
                ]
            }
        }
    }
};

// 平滑滚动到指定区域
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 加载示例案例
function loadExampleCase() {
    document.getElementById('case-description').value = exampleData.userInput.D_text;
    document.getElementById('litigation-claim').value = exampleData.userInput.C_claim;
    
    // 更新步骤状态
    updateStepStatus(2);
}

// 选择常见诉求
function selectClaim(claim) {
    const claimInput = document.getElementById('litigation-claim');
    if (claimInput.value.trim() === '') {
        claimInput.value = claim;
    } else {
        claimInput.value += '；' + claim;
    }
    updateStepStatus(2);
}

// 更新步骤状态
function updateStepStatus(activeStep) {
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        if (index + 1 <= activeStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// 开始分析
function startAnalysis() {
    const caseDescription = document.getElementById('case-description').value.trim();
    const litigationClaim = document.getElementById('litigation-claim').value.trim();
    
    if (!caseDescription || !litigationClaim) {
        alert('请填写完整的案情描述和诉讼诉求');
        return;
    }
    
    // 显示加载状态
    const analyzeBtn = document.querySelector('.analyze-btn');
    const btnText = analyzeBtn.querySelector('.btn-text');
    const loadingSpinner = analyzeBtn.querySelector('.loading-spinner');
    const analysisStatus = document.querySelector('.analysis-status');
    
    btnText.style.display = 'none';
    loadingSpinner.style.display = 'block';
    analysisStatus.style.display = 'block';
    analyzeBtn.disabled = true;
    
    // 模拟分析过程
    setTimeout(() => {
        // 隐藏加载状态
        btnText.style.display = 'inline';
        loadingSpinner.style.display = 'none';
        analysisStatus.style.display = 'none';
        analyzeBtn.disabled = false;
        
        // 显示结果区域
        showResults();
        
        // 滚动到结果区域
        scrollToSection('results');
    }, 3000);
}

// 显示分析结果
function showResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'block';
    
    // 生成文档内容
    generateDocument();
    
    // 生成分析报告
    generateAnalysisReport();
}

// 生成文档内容
function generateDocument() {
    const documentContent = document.getElementById('document-content');
    const doc = exampleData.systemOutput.L_document;
    
    let html = `
        <div class="document-title">${doc.title}</div>
        
        <div class="party-info">
            <p><strong>${doc.plaintiff.label}：</strong>${doc.plaintiff.name}，${doc.plaintiff.gender}，${doc.plaintiff.birthDate}生，${doc.plaintiff.ethnicity}族，身份证号：${doc.plaintiff.idNumber}，住址：${doc.plaintiff.address}，联系电话：${doc.plaintiff.phone}。</p>
            
            <p><strong>${doc.defendant.label}：</strong>${doc.defendant.name}，统一社会信用代码：${doc.defendant.creditCode}，住所地：${doc.defendant.address}，${doc.defendant.representativeTitle}：${doc.defendant.legalRepresentative}，联系电话：${doc.defendant.phone}。</p>
        </div>
        
        <div class="case-cause">
            <p><strong>案由：</strong>${doc.caseCause}</p>
        </div>
        
        <div class="claims-section">
            <h4>${doc.claims.title}：</h4>
            <ol>
                ${doc.claims.items.map(item => `<li>${item}</li>`).join('')}
            </ol>
        </div>
        
        <div class="facts-section">
            <h4>${doc.factsAndReasons.title}：</h4>
            ${doc.factsAndReasons.sections.map(section => `
                <div class="fact-section">
                    <h5>${section.heading}</h5>
                    <p>${section.content}</p>
                </div>
            `).join('')}
        </div>
        
        <div class="salutation">
            <p>${doc.salutation}</p>
            <p>${doc.courtName}</p>
            <br>
            <p style="text-align: right;">${doc.filer.label}${doc.filer.name}</p>
            <p style="text-align: right;">${doc.filingDate}</p>
        </div>
    `;
    
    documentContent.innerHTML = html;
}

// 生成分析报告
function generateAnalysisReport() {
    const analysis = exampleData.systemOutput.R_analysis;
    
    // 更新概览数据
    document.getElementById('case-strength-score').textContent = analysis.reportSummary.caseStrengthScore;
    document.getElementById('evidence-gaps').textContent = analysis.reportSummary.keyEvidenceGaps;
    document.getElementById('strategy-suggestions').textContent = analysis.reportSummary.strategySuggestions;
    
    // 生成证据分析
    generateEvidenceAnalysis(analysis);
    
    // 生成法律洞察
    generateLegalInsights(analysis);
    
    // 生成法庭推演
    generateCourtSimulation(analysis);
}

// 生成证据分析
function generateEvidenceAnalysis(analysis) {
    const gapDiagnosis = document.getElementById('gap-diagnosis');
    gapDiagnosis.innerHTML = `
        <div class="gap-item">
            <div class="gap-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="gap-content">
                <h4>证据缺口诊断</h4>
                <p>${analysis.evidenceAnalysis.gapDiagnosis[0]}</p>
            </div>
        </div>
    `;
}

// 生成法律洞察
function generateLegalInsights(analysis) {
    const statutesContainer = document.getElementById('relevant-statutes');
    const casesContainer = document.getElementById('guiding-cases');
    
    // 相关法律条款
    statutesContainer.innerHTML = analysis.legalInsights.relevantStatutes.map(statute => `
        <div class="statute-item">
            <h5>${statute.statuteName}</h5>
            <p>${statute.content}</p>
        </div>
    `).join('');
    
    // 指导案例
    casesContainer.innerHTML = analysis.legalInsights.guidingCases.map(case_ => `
        <div class="case-item">
            <h5>${case_.caseTitle}</h5>
            <p class="case-number">${case_.caseNumber}</p>
            <p>${case_.judgmentSummary}</p>
        </div>
    `).join('');
}

// 生成法庭推演
function generateCourtSimulation(analysis) {
    const ourArgumentsList = document.getElementById('our-arguments-list');
    const opponentDefensesList = document.getElementById('opponent-defenses-list');
    const strategyChecklist = document.getElementById('strategy-checklist');
    
    // 我方主张
    ourArgumentsList.innerHTML = analysis.courtSimulation.adversarialSummary.ourArguments.map(arg => `
        <div class="argument-item">
            <i class="fas fa-check-circle"></i>
            <span>${arg}</span>
        </div>
    `).join('');
    
    // 对方抗辩
    opponentDefensesList.innerHTML = analysis.courtSimulation.adversarialSummary.opponentDefenses.map(defense => `
        <div class="defense-item">
            <i class="fas fa-shield-alt"></i>
            <span>${defense}</span>
        </div>
    `).join('');
    
    // 策略建议
    strategyChecklist.innerHTML = analysis.courtSimulation.strategyOptimization.map((strategy, index) => `
        <div class="strategy-item">
            <div class="strategy-number">${index + 1}</div>
            <span>${strategy}</span>
        </div>
    `).join('');
}

// 切换标签页
function switchTab(tabName) {
    // 更新标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// 复制文档
function copyDocument() {
    const documentContent = document.getElementById('document-content');
    const text = documentContent.innerText;
    
    navigator.clipboard.writeText(text).then(() => {
        alert('文档内容已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择文本复制');
    });
}

// 下载文档
function downloadDocument() {
    const documentContent = document.getElementById('document-content');
    const text = documentContent.innerText;
    
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '民事起诉状.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 添加滚动动画效果
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    document.querySelectorAll('.value-item, .overview-card, .step, .gap-item, .statute-item, .case-item').forEach(el => {
        observer.observe(el);
    });
}

// 添加数字动画效果
function animateNumbers() {
    const scoreElement = document.getElementById('case-strength-score');
    const gapsElement = document.getElementById('evidence-gaps');
    const suggestionsElement = document.getElementById('strategy-suggestions');

    if (scoreElement) {
        animateNumber(scoreElement, 0, 78, 2000);
    }
    if (gapsElement) {
        animateNumber(gapsElement, 0, 1, 1000);
    }
    if (suggestionsElement) {
        animateNumber(suggestionsElement, 0, 3, 1500);
    }
}

// 数字动画函数
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * easeOutCubic(progress));
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// 缓动函数
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

// 添加打字机效果
function typewriterEffect(element, text, speed = 50) {
    element.textContent = '';
    let i = 0;

    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }

    type();
}

// 增强的显示结果函数
function showResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'block';

    // 生成文档内容
    generateDocument();

    // 生成分析报告
    generateAnalysisReport();

    // 添加动画效果
    setTimeout(() => {
        addScrollAnimations();
        animateNumbers();
    }, 500);
}

// 添加页面滚动进度指示器
function addScrollProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    progressBar.innerHTML = '<div class="scroll-progress-bar"></div>';
    document.body.appendChild(progressBar);

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        document.querySelector('.scroll-progress-bar').style.width = scrollPercent + '%';
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 监听输入框变化，更新步骤状态
    const caseDescription = document.getElementById('case-description');
    const litigationClaim = document.getElementById('litigation-claim');

    if (caseDescription) {
        caseDescription.addEventListener('input', function() {
            if (this.value.trim()) {
                updateStepStatus(1);
            }
        });
    }

    if (litigationClaim) {
        litigationClaim.addEventListener('input', function() {
            if (this.value.trim()) {
                updateStepStatus(2);
            }
        });
    }

    // 添加滚动动画
    addScrollAnimations();

    // 添加滚动进度条
    addScrollProgress();

    // 添加平滑滚动到顶部按钮
    const backToTop = document.createElement('button');
    backToTop.className = 'back-to-top';
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
    document.body.appendChild(backToTop);

    // 显示/隐藏返回顶部按钮
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.classList.add('show');
        } else {
            backToTop.classList.remove('show');
        }
    });

    // 绑定开始体验按钮
    const ctaButton = document.getElementById('start-experience-btn');
    if (ctaButton) {
        ctaButton.addEventListener('click', function(e) {
            e.preventDefault();
            scrollToSection('interactive');
        });
    }
});
