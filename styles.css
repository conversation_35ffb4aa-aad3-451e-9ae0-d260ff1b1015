/* 基础样式重置和全局设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 系统介绍区 (Hero Section) */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.animated-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.brand-name {
    color: #ffd700;
    display: inline-block;
    margin-right: 0.5rem;
}

.system-type {
    color: white;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.core-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.value-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.value-item i {
    font-size: 2rem;
    margin-right: 1rem;
    color: #ffd700;
}

.value-item span {
    font-size: 1.1rem;
    font-weight: 500;
}

.cta-button {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.cta-button i {
    transition: transform 0.3s ease;
}

.cta-button:hover i {
    transform: translateX(5px);
}

/* 智能交互区 */
.interactive-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.step-guide {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.step.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.05);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.step.active .step-number {
    background: white;
    color: #667eea;
}

.step-title {
    font-weight: 500;
}

.input-area {
    max-width: 800px;
    margin: 0 auto;
}

.input-group {
    margin-bottom: 2rem;
}

.input-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.input-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.input-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.example-btn {
    margin-top: 0.5rem;
    background: #28a745;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.example-btn:hover {
    background: #218838;
}

.common-claims {
    margin-top: 0.5rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.claim-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.claim-tag:hover {
    background: #667eea;
    color: white;
}

.analyze-section {
    text-align: center;
    margin-top: 3rem;
}

.analyze-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.analysis-status {
    margin-top: 1rem;
    color: #666;
    font-style: italic;
}

/* 成果展示区 */
.results-section {
    padding: 5rem 0;
    background: white;
}

.results-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #495057;
    padding: 1rem 2rem;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 文档容器 */
.document-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.document-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.document-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.action-btn:hover {
    background: #5a6fd8;
}

.document-content {
    padding: 2rem;
    font-size: 1rem;
    line-height: 1.8;
    color: #333;
}

/* 分析报告样式 */
.report-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.overview-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.card-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.score-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.2rem;
}

.score {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
}

.score-unit {
    font-size: 1.2rem;
    color: #666;
}

/* 价值总结区 */
.footer-section {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.value-summary h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
}

.value-summary p {
    font-size: 1.1rem;
    line-height: 1.8;
    opacity: 0.9;
}

.contact-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.2rem;
    color: #ffd700;
    width: 20px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    opacity: 0.7;
}

/* 证据分析样式 */
.evidence-analysis {
    margin-bottom: 3rem;
}

.evidence-analysis h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.evidence-graph {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    color: #666;
    font-style: italic;
}

.gap-diagnosis {
    margin-top: 2rem;
}

.gap-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
}

.gap-icon {
    color: #f39c12;
    font-size: 1.5rem;
    margin-top: 0.2rem;
}

.gap-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.gap-content p {
    color: #666;
    line-height: 1.6;
}

/* 法律洞察样式 */
.legal-insights {
    margin-bottom: 3rem;
}

.legal-insights h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.insights-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.statutes-section h4,
.cases-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.statute-item,
.case-item {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.statute-item h5,
.case-item h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.case-number {
    font-size: 0.9rem;
    color: #999;
    margin-bottom: 0.5rem;
    font-style: italic;
}

.statute-item p,
.case-item p {
    color: #666;
    line-height: 1.6;
}

/* 法庭推演样式 */
.court-simulation h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 2rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.arguments-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.our-arguments h4,
.opponent-defenses h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.our-arguments h4 {
    color: #28a745;
}

.opponent-defenses h4 {
    color: #dc3545;
}

.argument-item,
.defense-item {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.argument-item i {
    color: #28a745;
    margin-top: 0.2rem;
}

.defense-item i {
    color: #dc3545;
    margin-top: 0.2rem;
}

.strategy-optimization h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.strategy-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #e8f4fd;
    border-left: 4px solid #667eea;
    border-radius: 0 8px 8px 0;
    margin-bottom: 1rem;
}

.strategy-number {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

/* 文档样式 */
.document-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.party-info {
    margin-bottom: 1.5rem;
}

.party-info p {
    margin-bottom: 1rem;
    line-height: 1.8;
}

.case-cause {
    margin-bottom: 1.5rem;
}

.claims-section,
.facts-section {
    margin-bottom: 2rem;
}

.claims-section h4,
.facts-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.claims-section ol {
    padding-left: 2rem;
}

.claims-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.fact-section {
    margin-bottom: 1.5rem;
}

.fact-section h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 0.8rem;
}

.fact-section p {
    line-height: 1.8;
    text-indent: 2em;
}

.salutation {
    margin-top: 2rem;
    text-align: left;
}

/* 动画效果 */
.overview-card,
.gap-item,
.statute-item,
.case-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.value-item,
.step {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

.value-item.animate-in,
.overview-card.animate-in,
.step.animate-in,
.gap-item.animate-in,
.statute-item.animate-in,
.case-item.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* 滚动进度条 */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

.scroll-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    width: 0%;
    transition: width 0.3s ease;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 加载动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.analyze-btn:disabled {
    animation: pulse 1.5s infinite;
}

/* 渐入动画延迟 */
.value-item:nth-child(1) { transition-delay: 0.1s; }
.value-item:nth-child(2) { transition-delay: 0.2s; }
.value-item:nth-child(3) { transition-delay: 0.3s; }
.value-item:nth-child(4) { transition-delay: 0.4s; }

.overview-card:nth-child(1) { transition-delay: 0.1s; }
.overview-card:nth-child(2) { transition-delay: 0.2s; }
.overview-card:nth-child(3) { transition-delay: 0.3s; }

/* 悬停效果增强 */
.value-item:hover,
.overview-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.tab-btn:hover:not(.active) {
    transform: translateY(-2px);
}

.action-btn:hover,
.example-btn:hover,
.claim-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 焦点状态优化 */
.input-group textarea:focus,
.analyze-btn:focus,
.cta-button:focus,
.tab-btn:focus,
.action-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 选择状态 */
::selection {
    background: rgba(102, 126, 234, 0.2);
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-title {
        font-size: 3rem;
    }

    .core-values {
        grid-template-columns: repeat(2, 1fr);
    }

    .insights-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .arguments-section {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .core-values {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .value-item {
        padding: 1rem;
    }

    .value-item i {
        font-size: 1.5rem;
    }

    .step-guide {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .step {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .results-tabs {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .insights-content {
        grid-template-columns: 1fr;
    }

    .arguments-section {
        grid-template-columns: 1fr;
    }

    .report-overview {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .document-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }

    .container {
        padding: 0 15px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .interactive-section,
    .results-section {
        padding: 3rem 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .analyze-btn {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .value-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .value-item i {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .input-group textarea {
        font-size: 0.9rem;
    }

    .document-content {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .overview-card {
        padding: 1.5rem 1rem;
    }

    .score {
        font-size: 2.5rem;
    }

    .gap-item,
    .argument-item,
    .defense-item,
    .strategy-item {
        padding: 0.8rem;
    }

    .strategy-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .strategy-number {
        align-self: flex-start;
    }
}
